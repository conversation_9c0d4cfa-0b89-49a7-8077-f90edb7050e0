plugins {
    id 'java'
    id "com.diffplug.spotless" version "7.0.0.BETA2"
    id "jacoco"
}

group = 'io.hydrax.aeron'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenCentral()
    mavenLocal()
}

spotless {
    java {
        targetExclude("build/**")
        googleJavaFormat('1.23.0').reflowLongStrings().formatJavadoc(false).reorderImports(false).groupArtifact('com.google.googlejavaformat:google-java-format')
    }
}

dependencies {
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    implementation 'io.smallrye.config:smallrye-config:3.9.1'
    implementation 'io.smallrye.config:smallrye-config-source-yaml:3.9.1'
    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.34'
    testCompileOnly 'org.projectlombok:lombok:1.18.34'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.34'

    implementation 'ch.qos.logback:logback-classic:1.5.18'

    // aeron
    implementation 'io.aeron:aeron-all:1.43.0'
    implementation 'io.github.resilience4j:resilience4j-retry:2.3.0'
    implementation 'io.github.resilience4j:resilience4j-core:2.3.0'
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

jar {
    manifest {
        attributes 'Main-Class': 'io.hydrax.aeron.Main'  // 替换为你的主类全限定名
    }
    from {
        configurations.runtimeClasspath.collect {
            it.isDirectory() ? it : zipTree(it)
        }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.build {
    dependsOn 'spotlessApply'
}

compileTestJava {
    options.encoding = 'UTF-8'
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
}
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

test {
    useJUnitPlatform()
}

tasks.register('getImageTag') {
    println rootProject.name + ":" + (version as CharSequence)
}