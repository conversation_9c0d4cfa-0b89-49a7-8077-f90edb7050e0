package io.hydrax.aeron.service;

import static io.aeron.Aeron.NULL_VALUE;

import io.aeron.Aeron;
import io.aeron.ChannelUriStringBuilder;
import io.aeron.Publication;
import io.aeron.Subscription;
import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.codecs.SourceLocation;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import io.hydrax.aeron.common.CustomRecordingDescriptor;
import io.hydrax.aeron.config.AeronConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.AtomicBuffer;
import org.agrona.concurrent.UnsafeBuffer;

@Slf4j
@RequiredArgsConstructor
public class NoOpSubscriber implements FragmentHandler, Agent {

  //TODO help to update as a conf with defaul value instead
  private static final String SUBSCRIPTION_CHANNEL = "aeron:udp?endpoint=event-ledger:55888";
  private static final String MDC_CHANNEL =
      "aeron:udp?control=event-ledger:45888|control-mode=dynamic|ssc=true";

  final AeronConfigProperties aeronConfigProperties;
  Subscription subscription;
  Subscription spySubscription;
  Publication publication;
  CustomRecordingDescriptor recordingDescriptor = new CustomRecordingDescriptor();

  @Override
  public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
    publication.offer(buffer, offset, length);
  }

  public void start(int streamId) {
    Aeron.Context context =
        new Aeron.Context().aeronDirectoryName(aeronConfigProperties.directory());
    Aeron aeron = Aeron.connect(context);
    subscription = aeron.addSubscription(SUBSCRIPTION_CHANNEL, streamId);
    spySubscription =
        aeron.addSubscription(MDC_CHANNEL.replace("aeron:udp", "aeron-spy:aeron:udp"), streamId);
    try (AeronArchive aeronArchive =
        AeronArchive.connect(
            new AeronArchive.Context()
                .aeron(aeron)
                .controlRequestChannel(aeronConfigProperties.archive().controlChannel())
                .controlResponseChannel("aeron:udp?endpoint=localhost:15888"))) {
      aeronArchive.listRecordingsForUri(
          0,
          1000,
          MDC_CHANNEL,
          streamId,
          (controlSessionId,
              correlationId,
              recordingId,
              startTimestamp,
              stopTimestamp,
              startPosition,
              stopPosition,
              initialTermId,
              segmentFileLength,
              termBufferLength,
              mtuLength,
              sessionId,
              streamId1,
              strippedChannel,
              originalChannel,
              sourceIdentity) -> {
            log.info("Recording found: {}", recordingId);
            recordingDescriptor.set(
                controlSessionId,
                correlationId,
                recordingId,
                startTimestamp,
                stopTimestamp,
                startPosition,
                stopPosition,
                initialTermId,
                segmentFileLength,
                termBufferLength,
                mtuLength,
                sessionId,
                streamId1,
                strippedChannel,
                originalChannel,
                sourceIdentity);
          });
      if (recordingDescriptor.getRecordingId() == NULL_VALUE) {
        aeronArchive.startRecording(MDC_CHANNEL, streamId, SourceLocation.LOCAL, true);
        publication = aeron.addPublication(MDC_CHANNEL, streamId);
      } else {
        ChannelUriStringBuilder channelUriStringBuilder =
            new ChannelUriStringBuilder(MDC_CHANNEL).mtu(recordingDescriptor.getMtuLength());
        if (recordingDescriptor.getStopPosition() > 0) {
          log.info(
              "stop position: {}, initialTermId: {}, termBufferLength: {}",
              recordingDescriptor.getStopPosition(),
              recordingDescriptor.getInitialTermId(),
              recordingDescriptor.getTermBufferLength());
          if (recordingDescriptor.getStopPosition() > 0) {
            channelUriStringBuilder.initialPosition(
                recordingDescriptor.getStopPosition(),
                recordingDescriptor.getInitialTermId(),
                recordingDescriptor.getTermBufferLength());
          }
        }
        final String publicationExtendChannel = channelUriStringBuilder.build();
        extendRecording(aeronArchive, recordingDescriptor.getRecordingId(), streamId);
        publication = aeron.addPublication(publicationExtendChannel, streamId);
      }

      AtomicBuffer heartbeat = new UnsafeBuffer(new byte[8]);
      heartbeat.putLong(0, System.currentTimeMillis());
      long offered = publication.offer(heartbeat, 0, 8);
      log.debug("Heartbeat offered: {}", offered);
    } catch (Exception e) {
      log.error("Failed to start recording", e);
    }
    log.info(
        "Starting NoOpSubscriber: {}, {}, {}",
        subscription.channel(),
        subscription.channelStatus(),
        subscription.isConnected());
  }

  @Override
  public int doWork() {
    spySubscription.poll((buffer, o, l, header) -> {}, 100);
    return subscription.poll(this, 100);
  }

  @Override
  public String roleName() {
    return "no-op-subscriber";
  }

  private void extendRecording(AeronArchive aeronArchive, Long recordingId, int streamId) {
    try {
      log.info("Extending recording: {}, {}, {}", recordingId, MDC_CHANNEL, streamId);
      aeronArchive.extendRecording(recordingId, MDC_CHANNEL, streamId, SourceLocation.LOCAL, true);
    } catch (Exception e) {
      log.error("Failed to extend recording", e);
      throw e;
    }
  }
}
