package io.hydrax.aeron.convert;

import java.time.Duration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.eclipse.microprofile.config.spi.Converter;

public class DurationConverter implements Converter<Duration> {
  @Override
  public Duration convert(String value) {
    String s = value.trim().toLowerCase();
    Matcher matcher = Pattern.compile("^(\\d+)([smhd])$").matcher(s);
    if (matcher.find()) {
      long num = Long.parseLong(matcher.group(1));
      String unit = matcher.group(2);
      return switch (unit) {
        case "s" -> Duration.ofSeconds(num);
        case "m" -> Duration.ofMinutes(num);
        case "h" -> Duration.ofHours(num);
        case "d" -> Duration.ofDays(num);
        default -> throw new IllegalArgumentException("Invalid unit: " + unit);
      };
    }
    return Duration.parse(value); // 回退到 ISO 格式
  }
}
