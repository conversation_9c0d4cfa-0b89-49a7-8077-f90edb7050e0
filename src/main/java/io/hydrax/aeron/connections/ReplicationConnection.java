package io.hydrax.aeron.connections;

import io.aeron.Aeron;
import io.aeron.Subscription;
import io.aeron.archive.client.AeronArchive;
import io.aeron.exceptions.TimeoutException;
import io.hydrax.aeron.config.AeronConfigProperties;
import io.hydrax.aeron.exceoptions.ConnectionException;
import io.hydrax.aeron.recordevents.RecordEventsHandler;
import io.hydrax.aeron.recordevents.RecordingEventsSubscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.agrona.concurrent.AgentRunner;
import org.agrona.concurrent.BackoffIdleStrategy;

@RequiredArgsConstructor
@Slf4j
public class ReplicationConnection implements Runnable {

  private final AeronConfigProperties aeronConfig;

  @Override
  public void run() {
    Aeron aeron = null;
    AeronArchive aeronArchive = null;
    AgentRunner agentRunner = null;
    try {
      log.debug("Starting Replication Connection");
      aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(aeronConfig.directory()));
      log.debug("aeron connected");
      Subscription recordingEvents =
          aeron.addSubscription(aeronConfig.archive().recordingEventsChannel(), 1001);
      aeronArchive =
          AeronArchive.connect(
              new AeronArchive.Context()
                  .aeron(aeron)
                  .controlRequestChannel(aeronConfig.replication().requestChannel())
                  .controlRequestStreamId(AeronArchive.Configuration.controlStreamId())
                  .controlResponseChannel(aeronConfig.replication().responseChannel())
                  .controlResponseStreamId(aeronConfig.replication().responseStreamId()));
      RecordEventsHandler recordEventsHandler =
          new RecordEventsHandler(aeronConfig.replication(), aeronArchive);
      RecordingEventsSubscribe recordingEventsSubscribe =
          new RecordingEventsSubscribe(recordEventsHandler, recordingEvents);
      agentRunner =
          new AgentRunner(
              new BackoffIdleStrategy(),
              e -> log.error("Error in recording events agent", e),
              null,
              recordingEventsSubscribe);
      agentRunner.run();
    } catch (TimeoutException e) {
      log.error("Failed to connect to Aeron: ", e);
      throw new ConnectionException("connection error!");
    } finally {
      if (agentRunner != null) {
        agentRunner.close();
      }
      if (aeronArchive != null) {
        aeronArchive.close();
      }
      if (aeron != null) {
        aeron.close();
      }
    }
    log.info("events agent started");
  }
}
