package io.hydrax.aeron.common;

import lombok.Getter;

@Getter
public enum StreamIdEnum {
  SOR_ER(40),
  SOR_ORDER_BOOK(41),
  OV_OUT(42),
  MATCH_OUT(43),
  RFQ_OUT(44);

  private final int streamId;

  StreamIdEnum(int streamId) {
    this.streamId = streamId;
  }

  public static StreamIdEnum getByIndex(int index) {
    if (index < 0 || index >= StreamIdEnum.values().length) {
      throw new IllegalArgumentException("Invalid index: " + index);
    }
    return StreamIdEnum.values()[index];
  }
}
