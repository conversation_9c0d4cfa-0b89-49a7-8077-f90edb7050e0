package io.hydrax.aeron.common;

import static io.aeron.Aeron.NULL_VALUE;

import lombok.Getter;

@Getter
public class CustomRecordingDescriptor {
  private long controlSessionId;
  private long correlationId;
  private long recordingId = NULL_VALUE;
  private long startTimestamp;
  private long stopTimestamp;
  private long startPosition;
  private long stopPosition;
  private int initialTermId;
  private int segmentFileLength;
  private int termBufferLength;
  private int mtuLength;
  private int sessionId;
  private int streamId;
  private String strippedChannel;
  private String originalChannel;
  private String sourceIdentity;
  private boolean isRetained = false;

  public CustomRecordingDescriptor set(
      long controlSessionId,
      long correlationId,
      long recordingId,
      long startTimestamp,
      long stopTimestamp,
      long startPosition,
      long stopPosition,
      int initialTermId,
      int segmentFileLength,
      int termBufferLength,
      int mtuLength,
      int sessionId,
      int streamId,
      String strippedChannel,
      String originalChannel,
      String sourceIdentity) {

    this.controlSessionId = controlSessionId;
    this.correlationId = correlationId;
    this.recordingId = recordingId;
    this.startTimestamp = startTimestamp;
    this.stopTimestamp = stopTimestamp;
    this.startPosition = startPosition;
    this.stopPosition = stopPosition;
    this.initialTermId = initialTermId;
    this.segmentFileLength = segmentFileLength;
    this.termBufferLength = termBufferLength;
    this.mtuLength = mtuLength;
    this.sessionId = sessionId;
    this.streamId = streamId;
    this.strippedChannel = strippedChannel;
    this.originalChannel = originalChannel;
    this.sourceIdentity = sourceIdentity;
    this.isRetained = true;
    return this;
  }
}
