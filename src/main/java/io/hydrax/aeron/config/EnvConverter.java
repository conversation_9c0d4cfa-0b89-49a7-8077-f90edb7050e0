package io.hydrax.aeron.config;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.eclipse.microprofile.config.spi.Converter;

public class EnvConverter implements Converter<String> {
  private static final Pattern ENV_PATTERN = Pattern.compile("\\$\\{(.*?)}");

  public static String replaceEnvVariables(String input) {
    Matcher matcher = ENV_PATTERN.matcher(input);
    StringBuilder buffer = new StringBuilder();
    while (matcher.find()) {
      String envName = matcher.group(1);
      String envValue = System.getenv(envName);

      String replacement =
          (envValue != null) ? Matcher.quoteReplacement(envValue) : matcher.group(0);

      matcher.appendReplacement(buffer, replacement);
    }
    matcher.appendTail(buffer);
    return buffer.toString();
  }

  @Override
  public String convert(String s) throws IllegalArgumentException, NullPointerException {
    return replaceEnvVariables(s);
  }
}
