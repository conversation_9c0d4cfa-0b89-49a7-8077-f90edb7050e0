package io.hydrax.aeron.config;

import io.github.resilience4j.retry.RetryConfig;
import io.hydrax.aeron.exceoptions.ConnectionException;
import java.io.IOException;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class RetryConfiguration {
  final AeronConfigProperties.Retry retryProperties;

  public final RetryConfig retryConfig() {
    return RetryConfig.custom()
        .maxAttempts(retryProperties.attempts())
        .waitDuration(retryProperties.delay())
        .retryExceptions(IOException.class, ConnectionException.class)
        .build();
  }
}
