package io.hydrax.aeron.config;

import org.agrona.concurrent.*;
import org.eclipse.microprofile.config.spi.Converter;

public class IdleStrategyConverter implements Converter<IdleStrategy> {
  @Override
  public IdleStrategy convert(String value) throws IllegalArgumentException, NullPointerException {
    if (value == null) {
      return new BackoffIdleStrategy();
    }
    return switch (value) {
      case "busy_spin" -> new BusySpinIdleStrategy();
      case "noop" -> new NoOpIdleStrategy();
      case "yield" -> new YieldingIdleStrategy();
      case "sleep" -> new SleepingIdleStrategy();
      default -> new BackoffIdleStrategy();
    };
  }
}
