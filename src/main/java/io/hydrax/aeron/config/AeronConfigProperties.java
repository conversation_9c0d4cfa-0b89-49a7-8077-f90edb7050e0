package io.hydrax.aeron.config;

import io.smallrye.config.ConfigMapping;
import io.smallrye.config.WithConverter;
import io.smallrye.config.WithDefault;
import io.smallrye.config.WithName;
import java.time.Duration;
import org.agrona.concurrent.IdleStrategy;

/** Configuration properties for Aeron. Maps to the 'aeron' section in application.yml. */
@ConfigMapping(prefix = "aeron")
public interface AeronConfigProperties {

  /**
   * Gets the Aeron directory path.
   *
   * @return the directory path
   */
  @WithConverter(value = EnvConverter.class)
  String directory();

  @WithConverter(value = IdleStrategyConverter.class)
  IdleStrategy idleStrategy();

  /**
   * Gets the archive configuration.
   *
   * @return the archive configuration
   */
  Archive archive();

  Replication replication();

  Retry retry();

  /** Archive configuration properties. */
  interface Archive {
    /**
     * Gets the Aeron archive directory path.
     *
     * @return the archive directory path
     */
    @WithConverter(value = EnvConverter.class)
    String directory();

    /**
     * Gets the Aeron control channel.
     *
     * @return the control channel
     */
    @WithName("control-channel")
    @WithDefault("aeron:udp?endpoint=localhost:8010")
    @WithConverter(value = EnvConverter.class)
    String controlChannel();

    /**
     * Gets the Aeron recording events channel.
     *
     * @return the recording events channel
     */
    @WithName("recording-events-channel")
    @WithDefault("aeron:udp?endpoint=localhost:8011")
    @WithConverter(value = EnvConverter.class)
    String recordingEventsChannel();

    /**
     * Gets the Aeron replication channel.
     *
     * @return the replication channel
     */
    @WithName("replication-channel")
    @WithDefault("aeron:udp?endpoint=localhost:8012")
    @WithConverter(value = EnvConverter.class)
    String replicationChannel();
  }

  interface Replication {

    @WithConverter(value = EnvConverter.class)
    String requestChannel();

    @WithConverter(value = EnvConverter.class)
    String responseChannel();

    @WithDefault("1002")
    int requestStreamId();

    @WithDefault("1003")
    int responseStreamId();
  }

  interface Retry {
    @WithDefault("3")
    int attempts();

    @WithDefault("PT2S")
    Duration delay();
  }
}
