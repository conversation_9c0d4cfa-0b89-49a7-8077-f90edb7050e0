package io.hydrax.aeron;

import io.aeron.archive.Archive;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.hydrax.aeron.common.StreamIdEnum;
import io.hydrax.aeron.config.AeronConfigProperties;
import io.hydrax.aeron.config.RetryConfiguration;
import io.hydrax.aeron.connections.ReplicationConnection;
import io.hydrax.aeron.exceoptions.ConnectionException;
import io.hydrax.aeron.service.NoOpSubscriber;
import io.smallrye.config.SmallRyeConfig;
import io.smallrye.config.SmallRyeConfigBuilder;
import lombok.extern.slf4j.Slf4j;
import org.agrona.concurrent.AgentRunner;

@Slf4j
public class Main {

  public static void main(String[] args) {
    log.info("Starting Aeron application.");
    AeronConfigProperties aeronConfig = loadConfiguration();
    try {
      MediaDriver ignoredMD = initializeMediaDriver(aeronConfig);
      Archive ignoredArchive = initializeArchive(aeronConfig);
      log.info("MediaDriver and Archive initialized successfully.");
      runNoOpSubscribers(aeronConfig);
      //      runReplicationConnections(aeronConfig);
      log.info("Application execution completed successfully.");
    } catch (InterruptedException e) {
      log.error("Execution interrupted: ", e);
      Thread.currentThread().interrupt();
      System.exit(1);
    } catch (ConnectionException e) {
      log.error("Replication connection failed: ", e);
      System.exit(1);
    } catch (Exception e) {
      log.error("Unexpected error during application execution: ", e);
      System.exit(1);
    }
  }

  private static AeronConfigProperties loadConfiguration() {
    SmallRyeConfig config =
        new SmallRyeConfigBuilder()
            .addDefaultSources()
            .addDiscoveredSources()
            .withMapping(AeronConfigProperties.class)
            .build();
    return config.getConfigMapping(AeronConfigProperties.class);
  }

  private static MediaDriver initializeMediaDriver(AeronConfigProperties aeronConfig) {
    MediaDriver.Context mediaDriverContext =
        new MediaDriver.Context()
            .aeronDirectoryName(aeronConfig.directory())
            .dirDeleteOnStart(true)
            .threadingMode(ThreadingMode.DEDICATED);
    return MediaDriver.launch(mediaDriverContext);
  }

  private static Archive initializeArchive(AeronConfigProperties aeronConfig)
      throws InterruptedException {
    Archive.Context archiveContext =
        new Archive.Context()
            .errorHandler(e -> log.error("Error in archive: ", e))
            .aeronDirectoryName(aeronConfig.directory())
            .archiveDirectoryName(aeronConfig.archive().directory())
            .controlChannel(aeronConfig.archive().controlChannel())
            .recordingEventsChannel(aeronConfig.archive().recordingEventsChannel())
            .replicationChannel(aeronConfig.archive().replicationChannel());
    int retryCount = 0;
    Archive archive = null;
    while (retryCount < 3) {
      try {
        archive = Archive.launch(archiveContext);
        break;
      } catch (Exception e) {
        Thread.sleep(10000);
        retryCount++;
        log.error("Failed to start archive, will retry, count: {}", retryCount, e);
        if (retryCount >= 3) {
          log.error("Failed to start archive after 3 attempts, exiting");
          System.exit(1);
        }
      }
    }
    return archive;
  }

  private static void runNoOpSubscribers(AeronConfigProperties aeronConfig) {
    try {
      Thread.sleep(5000);
    } catch (InterruptedException e) {
      log.error("Error during sleep: ", e);
      Thread.currentThread().interrupt();
    }
    for (int i = 0; i < StreamIdEnum.values().length; i++) {
      NoOpSubscriber noOpSubscriber = new NoOpSubscriber(aeronConfig);
      int streamId = 0;
      try {
        streamId = StreamIdEnum.getByIndex(i).getStreamId();
      } catch (Exception e) {
        System.exit(255);
      }
      noOpSubscriber.start(streamId);

      AgentRunner noOpSubscriberRunner =
          new AgentRunner(
              aeronConfig.idleStrategy(),
              e -> log.error("Error in no op subscriber: {}", e.getStackTrace(), e),
              null,
              noOpSubscriber);
      AgentRunner.startOnThread(noOpSubscriberRunner);
    }
  }

  private static void runReplicationConnections(AeronConfigProperties aeronConfig) {
    RetryRegistry registry =
        RetryRegistry.of(new RetryConfiguration(aeronConfig.retry()).retryConfig());
    Retry retry = registry.retry("replicationRetry");
    while (true) {
      try {
        retry.decorateRunnable(new ReplicationConnection(aeronConfig)).run();
      } catch (ConnectionException e) {
        log.error("Connection error: ", e);
      } catch (Exception e) {
        log.error("Connection error: ", e);
        break;
      }
    }
  }
}
