package io.hydrax.aeron.recordevents;

import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.client.RecordingEventsListener;
import io.hydrax.aeron.config.AeronConfigProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class RecordEventsHandler implements RecordingEventsListener {

  private final AeronConfigProperties.Replication replicationConfig;
  private final AeronArchive aeronArchive;

  @Override
  public void onStart(
      long recordingId,
      long startPosition,
      int sessionId,
      int streamId,
      String channel,
      String sourceIdentity) {
    log.debug(
        "Recording started: recordingId={}, startPosition={}, sessionId={}, streamId={},"
            + " channel={}, sourceIdentity={}",
        recordingId,
        startPosition,
        sessionId,
        streamId,
        channel,
        sourceIdentity);
    aeronArchive.replicate(
        recordingId,
        recordingId,
        AeronArchive.Configuration.CONTROL_STREAM_ID_DEFAULT,
        replicationConfig.requestChannel(),
        channel);
  }

  @Override
  public void onProgress(long recordingId, long startPosition, long position) {}

  @Override
  public void onStop(long recordingId, long startPosition, long stopPosition) {
    log.debug(
        "Recording stopped: recordingId={}, startPosition={}, stopPosition={}",
        recordingId,
        startPosition,
        stopPosition);
  }
}
