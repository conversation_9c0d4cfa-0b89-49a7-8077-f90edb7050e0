package io.hydrax.aeron.recordevents;

import io.aeron.Subscription;
import io.aeron.archive.client.ArchiveException;
import io.aeron.archive.client.RecordingEventsListener;
import io.aeron.archive.codecs.MessageHeaderDecoder;
import io.aeron.archive.codecs.RecordingProgressDecoder;
import io.aeron.archive.codecs.RecordingStartedDecoder;
import io.aeron.archive.codecs.RecordingStoppedDecoder;
import io.aeron.logbuffer.Header;
import lombok.RequiredArgsConstructor;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;

@RequiredArgsConstructor
public class RecordingEventsSubscribe implements Agent {

  private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();
  private final RecordingStartedDecoder recordingStartedDecoder = new RecordingStartedDecoder();
  private final RecordingProgressDecoder recordingProgressDecoder = new RecordingProgressDecoder();
  private final RecordingStoppedDecoder recordingStoppedDecoder = new RecordingStoppedDecoder();
  private final RecordingEventsListener listener;
  private final Subscription subscription;

  @Override
  public int doWork() throws Exception {
    subscription.poll(this::onFragment, 100);
    return 1;
  }

  @Override
  public String roleName() {
    return "recording-events-subscribe";
  }

  public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
    messageHeaderDecoder.wrap(buffer, offset);

    final int schemaId = messageHeaderDecoder.schemaId();
    if (schemaId != MessageHeaderDecoder.SCHEMA_ID) {
      throw new ArchiveException(
          "expected schemaId=" + MessageHeaderDecoder.SCHEMA_ID + ", actual=" + schemaId);
    }

    switch (messageHeaderDecoder.templateId()) {
      case RecordingStartedDecoder.TEMPLATE_ID:
        recordingStartedDecoder.wrap(
            buffer,
            offset + MessageHeaderDecoder.ENCODED_LENGTH,
            messageHeaderDecoder.blockLength(),
            messageHeaderDecoder.version());

        listener.onStart(
            recordingStartedDecoder.recordingId(),
            recordingStartedDecoder.startPosition(),
            recordingStartedDecoder.sessionId(),
            recordingStartedDecoder.streamId(),
            recordingStartedDecoder.channel(),
            recordingStartedDecoder.sourceIdentity());
        break;

      case RecordingProgressDecoder.TEMPLATE_ID:
        recordingProgressDecoder.wrap(
            buffer,
            offset + MessageHeaderDecoder.ENCODED_LENGTH,
            messageHeaderDecoder.blockLength(),
            messageHeaderDecoder.version());

        listener.onProgress(
            recordingProgressDecoder.recordingId(),
            recordingProgressDecoder.startPosition(),
            recordingProgressDecoder.position());
        break;

      case RecordingStoppedDecoder.TEMPLATE_ID:
        recordingStoppedDecoder.wrap(
            buffer,
            offset + MessageHeaderDecoder.ENCODED_LENGTH,
            messageHeaderDecoder.blockLength(),
            messageHeaderDecoder.version());

        listener.onStop(
            recordingStoppedDecoder.recordingId(),
            recordingStoppedDecoder.startPosition(),
            recordingStoppedDecoder.stopPosition());
        break;
      default:
        throw new IllegalStateException("Unexpected value: " + messageHeaderDecoder.templateId());
    }
  }
}
