FROM registry.access.redhat.com/ubi8/openjdk-17:1.21-1.1744797574

COPY --from=hengyunabc/arthas:3.7.1 /opt/arthas /opt/arthas
COPY --chown=185 build/libs/exchange-generic-aeron-archive-*.jar /app/app.jar
COPY --chown=185 aeron/aeron-all-1.43.0.jar /aeron/

ENV JDK_JAVA_OPTIONS="--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED -Dsmallrye.config.locations=file:/app/config"

ENTRYPOINT ["java", "-jar", "/app/app.jar"]