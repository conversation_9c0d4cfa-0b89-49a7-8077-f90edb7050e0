FROM registry.access.redhat.com/ubi8/openjdk-17:1.21-1.1744797574

COPY --from=hengyunabc/arthas:3.7.1 /opt/arthas /opt/arthas
COPY --chown=185 build/libs/exchange-generic-aeron-archive-*.jar /app/app.jar
COPY --chown=185 aeron/aeron-all-1.43.0.jar /aeron/


# Environment variables for Kubernetes deployment
ENV JDK_JAVA_OPTIONS="--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED -Dsmallrye.config.locations=file:/app/config"
ENV SPRING_PROFILES_ACTIVE=k8s
ENV LOGGING_CONFIG=classpath:logback-k8s.xml

# Ensure stdout/stderr are not buffered
ENV JAVA_OPTS="-Djava.awt.headless=true -Dfile.encoding=UTF-8"

ENTRYPOINT ["java", "-jar", "/app/app.jar"]